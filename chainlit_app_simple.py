"""Chainlit + LangGraph Copilot with Plotly support
====================================================
Version corrigée - Résout les problèmes d'affichage des graphiques,
améliore le streaming et la gestion des erreurs.
"""

###############################################################################
# 0. Standard library imports & environment tweaks
###############################################################################

import os
import sys
import uuid
import json
import pickle
import asyncio
import pprint
from typing import Dict, List, Any, Optional

# Make sure Python streams are unbuffered so print() shows up immediately.
# (Works only when the process is launched by `python -u`, but we do our part.)
os.environ.setdefault("PYTHONUNBUFFERED", "1")

###############################################################################
# 1. Third‑party imports  
###############################################################################

from dotenv import load_dotenv

import chainlit as cl
from chainlit.types import ThreadDict
from chainlit.data.sql_alchemy import SQLAlchemyDataLayer

from langchain_core.messages import HumanMessage, ToolMessage, AIMessage
from langchain_core.runnables import RunnableConfig
from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver

# Local modules (your original project structure)
from main_copilot import (
    create_agent,
    make_postgres_checkpointer,  # still used elsewhere
    AgentState,
    InputData,
    _merge,
    ensure_message_ids,
    remove_duplicate_messages,
    get_prompt_for_partner,
)
from sandbox_client import SandboxClient

###############################################################################
# 2. Environment & data‑layer initialisation
###############################################################################

print("Attempting to load .env from CWD…", flush=True)
load_dotenv(override=True)

DB_HOST = os.getenv("POSTGRES_HOST", "localhost")
DB_PORT = os.getenv("POSTGRES_PORT", "5432")
DB_NAME = os.getenv("POSTGRES_DB", "chainlit_db")
DB_USER = os.getenv("POSTGRES_USER", "user_test")
DB_PASSWORD = os.getenv("POSTGRES_PASSWORD", "password_test")
DB_URI_LANGGRAPH = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

# ---------------------------------------------------------------------------
@cl.data_layer
def get_data_layer():
    conninfo = (
        "postgresql+asyncpg://" f"{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
    )
    print("[DEBUG CL DATA_LAYER] Initialising SQLAlchemyDataLayer…", flush=True)
    return SQLAlchemyDataLayer(conninfo=conninfo, storage_provider=None)

###############################################################################
# 3. OAuth callback (unchanged)
###############################################################################

@cl.oauth_callback
def oauth_callback(
    provider_id: str,
    token: str,
    raw_user_data: Dict[str, str],
    default_user: cl.User,
) -> Optional[cl.User]:
    print(f"OAuth callback for provider: {provider_id}", flush=True)
    return default_user

###############################################################################
# 4. Helpers
###############################################################################

def _parse_tool_content(content: Any) -> Dict[str, Any]:
    if isinstance(content, str):
        try: return json.loads(content)
        except json.JSONDecodeError: return {"raw_content": content, "error": "Not valid JSON"}
    if isinstance(content, dict): return content
    return {"raw_content": str(content), "error": "Unknown content type"}


def serialise_state(state: AgentState) -> dict: # From your code
    def _msg_to_dict(m):
        if isinstance(m, (HumanMessage, AIMessage, ToolMessage)):
            return {"type": m.__class__.__name__, "id": getattr(m, "id", None), "content": m.content if isinstance(m.content, str) else "<complex>"}
        return str(m)
    serialized = dict(state)
    if "messages" in serialized: serialized["messages"] = [_msg_to_dict(x) for x in serialized["messages"]]
    if "output_image_paths" in serialized: serialized["output_image_paths"] = list(serialized.get("output_image_paths",[])) # Ensure it's a list
    return serialized

###############################################################################
# 5. LangGraph initialisation helper (unchanged except print flush)
###############################################################################

async def initialize_langgraph_components(thread_id: str, partner_name: str):
    """Create/restore checkpointer + agent + state for the given thread."""

    print(f"[DEBUG LG] Initialising components for thread_id={thread_id}", flush=True)

    # 5.1  Checkpointer context manager ------------------------------------------------
    checkpointer_cm = AsyncPostgresSaver.from_conn_string(DB_URI_LANGGRAPH)
    try:
        cp_instance = await checkpointer_cm.__aenter__()
        await cp_instance.setup()
    except Exception as exc:  # noqa: BLE001
        print(f"[ERROR LG] Checkpointer setup failed: {exc}", flush=True)
        cp_instance = None

    cl.user_session.set("lg_checkpointer_cm", checkpointer_cm)
    cl.user_session.set("lg_checkpointer_instance", cp_instance)

    # 5.2  Agent ----------------------------------------------------------------------
    if cp_instance:
        lg_agent = create_agent(checkpointer=cp_instance, partner=partner_name)
        cl.user_session.set("lg_agent", lg_agent)
    else:
        cl.user_session.set("lg_agent", None)

    # 5.3  Agent state ----------------------------------------------------------------
    if cp_instance:
        cfg = RunnableConfig(configurable={"thread_id": thread_id})
        try:
            persisted = await cp_instance.aget(cfg) or {}
        except Exception:  # noqa: BLE001
            persisted = {}
    else:
        persisted = {}

    if persisted:
        # rebuild messages list into proper objects
        rebuilt: list[Any] = []
        for md in persisted.get("messages", []):
            if isinstance(md, (HumanMessage, AIMessage, ToolMessage)):
                rebuilt.append(md)
            elif isinstance(md, dict):
                typ = md.get("type", "").lower()
                if "human" in typ:
                    rebuilt.append(HumanMessage(**md))
                elif "ai" in typ:
                    rebuilt.append(AIMessage(**md))
                elif "tool" in typ:
                    rebuilt.append(ToolMessage(**md))
        curr_state: AgentState = {
            **persisted,
            "messages": rebuilt,
            "conversation_id": thread_id,
            "session_id": thread_id,
            "partner": partner_name,
        }  # type: ignore[assignment]
    else:
        curr_state = {
            "messages": [],
            "remaining_steps": 25,
            "input_data": [],
            "intermediate_outputs": [],
            "current_variables": {},
            "output_image_paths": [],
            "data_description": [],
            "generic_parser_request": [],
            "conversation_id": thread_id,
            "session_id": thread_id,
            "partner": partner_name,
            "partner_config": {},
            "summary": "",
            "id_last_summary": None,
        }  # type: ignore[assignment]

    cl.user_session.set("lg_agent_state", curr_state)
    cl.user_session.set("thread_id", thread_id)
    cl.user_session.set(
        "langgraph_initialized_for_thread", bool(cp_instance)
    )

###############################################################################
# 6. Chat‑lifecycle callbacks
###############################################################################

@cl.on_chat_start
async def on_chat_start():
    pn = os.getenv("DEFAULT_PARTNER", "oksigen")
    cl.user_session.set("partner_name", pn)
    cl.user_session.set("langgraph_initialized_for_thread", False)
    await cl.Message(content=f"Agent initialisé (Partenaire {pn}). Dites‑moi …").send()


@cl.on_chat_resume
async def on_chat_resume(thread: ThreadDict):
    tid = thread["id"]
    pn = thread.get("metadata", {}).get("partner_name", os.getenv("DEFAULT_PARTNER", "oksigen"))
    await initialize_langgraph_components(tid, pn)
    st = cl.user_session.get("lg_agent_state", {"messages": []})
    await cl.Message(
        content=f"Conversation reprise (Partenaire {pn}). {len(st.get('messages', []))} messages enregistrés."
    ).send()

###############################################################################
# 7. Main on_message handler  
###############################################################################

# Clés utilisées pour identifier les graphiques dans les payloads des outils
IMAGE_KEYS = ("output_image_paths", "plots", "IMAGES tool", "IMAGES")


@cl.on_message
async def on_message(msg_event: cl.Message): # Renamed incoming message to msg_event
    active_thread_id = cl.context.session.thread_id
    if not active_thread_id:
        print(f"[CRITICAL CL] cl.context.session.thread_id is None in on_message!", flush=True)
        await cl.Message(content="Erreur critique: Impossible d'identifier la session.").send()
        cl.user_session.set("langgraph_initialized_for_thread", False)
        return

    # Initialize LangGraph components if not already done for this thread_id
    if not cl.user_session.get("langgraph_initialized_for_thread") or \
       cl.user_session.get("thread_id") != active_thread_id:
        print(f"[DEBUG CL] Initializing/Re-syncing LangGraph for thread_id: {active_thread_id}", flush=True)
        partner_name_for_init = cl.user_session.get("partner_name", os.getenv("DEFAULT_PARTNER", "oksigen"))
        await initialize_langgraph_components(active_thread_id, partner_name_for_init)

    lg_agent = cl.user_session.get("lg_agent")
    # lg_agent_state is the state that WE will manage and update in the session.
    # It starts as the state from the beginning of the turn.
    lg_agent_state: Optional[AgentState] = cl.user_session.get("lg_agent_state")
    partner_name = cl.user_session.get("partner_name")

    print(f"\n[DEBUG CL] === Turn Start for Thread: {active_thread_id}, User Msg ID: {msg_event.id} ===", flush=True)

    # Get or create SandboxClient (now the async version)
    sandbox_client: Optional[SandboxClient] = cl.user_session.get("sandbox_client")
    if not sandbox_client:
        try:
            sandbox_client = SandboxClient() # Instantiates the async version
            cl.user_session.set("sandbox_client", sandbox_client)
            print("[DEBUG CL] Async SandboxClient instantiated.", flush=True)
        except Exception as e:
            print(f"[ERROR CL] Failed to instantiate SandboxClient: {e}", flush=True)
            await cl.Message(content="Erreur de configuration du client Sandbox.").send()
            return
    
    # Ensure sandbox_client is not None for type checkers if used later unconditionally
    if not sandbox_client: # Should not happen if above logic is correct
        await cl.Message(content="Erreur critique: Client Sandbox non disponible.").send()
        return


    if not lg_agent or not lg_agent_state or not partner_name:
        await cl.Message(content="Erreur: Agent non initialisé. Veuillez rafraîchir.").send()
        print(f"[ERROR CL] Crucial LangGraph components missing for thread {active_thread_id}.", flush=True)
        return

    human_message_obj = HumanMessage(content=msg_event.content, id=str(uuid.uuid4()))
    current_messages_for_input = list(lg_agent_state.get("messages", []))
    messages_for_lg_agent_input = current_messages_for_input + [human_message_obj]

    config_for_run = RunnableConfig(
        configurable={"thread_id": active_thread_id, "session_id": active_thread_id, "partner": partner_name}
    )

    async with cl.Step(
        name="Agent Processing", type="run", parent_id=msg_event.id, id=f"agent_proc_{uuid.uuid4().hex[:4]}"
    ) as agent_processing_step:
        agent_processing_step.input = msg_event.content
        await agent_processing_step.send()
        print(f"[DEBUG CL] 'Agent Processing' step (ID: {agent_processing_step.id}) sent.", flush=True)

        assistant_final_msg = cl.Message(content="", author="Assistant", parent_id=agent_processing_step.id)
        streamed_text_content = ""
        
        collected_plot_elements_this_turn: List[cl.Plotly] = []
        processed_plot_filenames_this_turn = set()
        processed_tool_ids_this_turn = set()
        
        final_graph_state_dict: Optional[dict] = None 

        try:
            # current_run_lg_agent_state_dict = None # Removed as final_graph_state_dict serves this
            # messages_from_current_run = [] # This will be derived from final_graph_state_dict

            async for chunk in lg_agent.astream({"messages": messages_for_lg_agent_input}, config=config_for_run, stream_mode="updates"):
                # print(f"[DEBUG CHUNK]: {chunk}", flush=True) # Very verbose, enable if needed
                final_graph_state_dict = chunk # The last chunk in "updates" mode is the final state

                # --- Extract messages from the current chunk for immediate processing (streaming, tool display) ---
                messages_in_current_chunk_for_display = []
                if isinstance(chunk, dict):
                    # Check various common places where 'messages' might be in a chunk
                    if "messages" in chunk: messages_in_current_chunk_for_display = chunk["messages"]
                    elif "agent" in chunk and isinstance(chunk.get("agent"), dict) and "messages" in chunk["agent"]:
                        messages_in_current_chunk_for_display = chunk["agent"]["messages"]
                    elif "tools" in chunk and isinstance(chunk.get("tools"), dict) and "messages" in chunk["tools"]:
                         messages_in_current_chunk_for_display = chunk["tools"]["messages"]
                
                # --- Stream AIMessage content ---
                if messages_in_current_chunk_for_display:
                    for msg_obj in reversed(messages_in_current_chunk_for_display): # Find last AI message
                        if isinstance(msg_obj, AIMessage):
                            # ... (Your AI content streaming logic - seems okay)
                            ai_content_to_stream = ""
                            if isinstance(msg_obj.content, str): ai_content_to_stream = msg_obj.content
                            elif isinstance(msg_obj.content, list): # complex content
                                for part in msg_obj.content:
                                    if isinstance(part, dict) and part.get("type") == "text": ai_content_to_stream += part.get("text", "")
                                    elif isinstance(part, str): ai_content_to_stream += part
                            
                            if ai_content_to_stream and ai_content_to_stream != streamed_text_content:
                                delta = ai_content_to_stream
                                if streamed_text_content and ai_content_to_stream.startswith(streamed_text_content):
                                    delta = ai_content_to_stream[len(streamed_text_content):]
                                elif not streamed_text_content: pass # First stream
                                else: # Content changed completely, reset stream
                                    assistant_final_msg.content = ""; streamed_text_content = ""
                                
                                if delta:
                                    if not assistant_final_msg.id: await assistant_final_msg.send()
                                    await assistant_final_msg.stream_token(delta)
                                    streamed_text_content += delta
                            break # Processed last AIMessage for streaming

                # --- Process ToolMessages from the current chunk ---
                if messages_in_current_chunk_for_display:
                    for msg_obj in messages_in_current_chunk_for_display:
                        if isinstance(msg_obj, ToolMessage):
                            tool_msg_id = msg_obj.id or f"tool_{msg_obj.name}_{hash(str(msg_obj.content))}"
                            if tool_msg_id in processed_tool_ids_this_turn: continue
                            processed_tool_ids_this_turn.add(tool_msg_id)

                            tool_name = msg_obj.name or "UnknownTool"
                            tool_payload = _parse_tool_content(msg_obj.content)
                            print(f"[DEBUG TOOL MSG] Tool: {tool_name}, Payload Keys: {list(tool_payload.keys()) if isinstance(tool_payload, dict) else 'Non-dict'}", flush=True)

                            async with cl.Step(name=f"Tool: {tool_name}", type="tool", parent_id=agent_processing_step.id) as tool_step:
                                tool_step.input = json.dumps(tool_payload.get("tool_input", tool_payload.get("args", {})), indent=2)
                                display_parts = [f"Tool **{tool_name}** executed."]
                                # ... (Your tool-specific text formatting for display_parts - seems okay)
                                if isinstance(tool_payload, dict):
                                    if tool_payload.get("status"): display_parts.append(f"Status: {tool_payload['status']}")
                                    if tool_name == "data_retriever":
                                        if tool_payload.get("status") == "success": display_parts = [f"**Data Retrieved**: {tool_payload.get('input_data',[])}"]
                                        else: display_parts = [f"**Data Retrieval Failed**: {tool_payload.get('error','Unknown')}"]
                                    elif tool_name == "complete_python_task":
                                        py_stdout = tool_payload.get("stdout","")
                                        display_parts = [f"**Python Output**:\n```\n{py_stdout}\n```"] # Simplified for brevity
                                    if tool_name not in ["data_retriever", "complete_python_task"]: # Generic
                                        if tool_payload.get("stdout"): display_parts.append(f"Output:\n```\n{tool_payload['stdout']}\n```")
                                        if tool_payload.get("stderr"): display_parts.append(f"Errors:\n```\n{tool_payload['stderr']}\n```")

                                # --- Plot processing directly from this tool_payload ---
                                current_tool_plot_filenames = []
                                if isinstance(tool_payload, dict):
                                    for key in IMAGE_KEYS:
                                        if key in tool_payload and isinstance(tool_payload[key], list):
                                            current_tool_plot_filenames.extend(fn for fn in tool_payload[key] if isinstance(fn, str))
                                
                                unique_tool_plot_filenames = list(dict.fromkeys(current_tool_plot_filenames))

                                if unique_tool_plot_filenames and sandbox_client: # Ensure sandbox_client is not None
                                    print(f"[DEBUG PLOT TOOL PAYLOAD] Tool '{tool_name}' payload has {len(unique_tool_plot_filenames)} plots: {unique_tool_plot_filenames}", flush=True)
                                    for plot_fn in unique_tool_plot_filenames:
                                        if not plot_fn.endswith((".pkl", ".pickle")): continue
                                        if plot_fn in processed_plot_filenames_this_turn: continue
                                        try:
                                            # download_plot is now async
                                            pickle_bytes = await sandbox_client.download_plot(session_id=active_thread_id, plot_name=plot_fn)
                                            if pickle_bytes:
                                                fig_obj = pickle.loads(pickle_bytes) # Synchronous
                                                if hasattr(fig_obj, 'to_dict') or isinstance(fig_obj, dict):
                                                    plot_elem = cl.Plotly(name=os.path.basename(plot_fn).rsplit('.',1)[0], figure=fig_obj, display="inline")
                                                    tool_step.elements.append(plot_elem)
                                                    collected_plot_elements_this_turn.append(plot_elem)
                                                    processed_plot_filenames_this_turn.add(plot_fn)
                                                    print(f"[DEBUG PLOT TOOL PAYLOAD] Added plot '{plot_fn}' from tool payload.", flush=True)
                                                else: display_parts.append(f"\n(Note: Invalid figure for {plot_fn})")
                                            else: display_parts.append(f"\n(Note: Failed to load {plot_fn})")
                                        except Exception as e_plot_tool:
                                            print(f"[ERROR PLOT TOOL PAYLOAD] Error with {plot_fn}: {e_plot_tool}", flush=True)
                                            display_parts.append(f"\n(Error displaying {plot_fn}: {e_plot_tool})")
                                
                                tool_step.output = "\n".join(display_parts)
                                await tool_step.update()

                                # --- Update the *session-managed* lg_agent_state here ---
                                # This ensures our view of the state (for the fallback plot check) is up-to-date
                                if isinstance(tool_payload, dict):
                                    if "input_data" in tool_payload and isinstance(tool_payload["input_data"], list):
                                        current_input_data_s = lg_agent_state.get("input_data", [])
                                        new_data_o = [InputData(**item) if isinstance(item, dict) else item for item in tool_payload["input_data"]]
                                        lg_agent_state["input_data"] = current_input_data_s + new_data_o # type: ignore

                                    tool_payload_plot_paths = []
                                    for key in IMAGE_KEYS:
                                        if key in tool_payload and isinstance(tool_payload[key], list):
                                            tool_payload_plot_paths.extend(fn for fn in tool_payload[key] if isinstance(fn, str))
                                    if tool_payload_plot_paths:
                                        existing_paths = lg_agent_state.get("output_image_paths", [])
                                        lg_agent_state["output_image_paths"] = list(set(existing_paths + tool_payload_plot_paths)) # type: ignore
                                        print(f"[DEBUG LG_AGENT_STATE ON_MSG] Updated output_image_paths from tool: {lg_agent_state['output_image_paths']}", flush=True)


            # ---- End of agent.astream() loop ----

            # ---- Fallback Plot Check: Using the session-managed lg_agent_state ----
            # This lg_agent_state should have been updated by tool message processing above.
            if lg_agent_state and sandbox_client:
                state_plot_paths_to_check = lg_agent_state.get("output_image_paths", [])
                print(f"[DEBUG PLOT FALLBACK] Checking session-managed lg_agent_state for plots. Paths: {state_plot_paths_to_check}", flush=True)
                
                for plot_filename in state_plot_paths_to_check:
                    if not plot_filename or not isinstance(plot_filename, str) or not plot_filename.endswith((".pkl", ".pickle")):
                        print(f"[WARN PLOT FALLBACK] Invalid filename in state: {plot_filename}", flush=True)
                        continue
                    if plot_filename in processed_plot_filenames_this_turn: # Already handled from direct tool payload
                        print(f"[DEBUG PLOT FALLBACK] Plot already processed this turn: {plot_filename}", flush=True)
                        continue
                    try:
                        print(f"[DEBUG PLOT FALLBACK] Processing plot from lg_agent_state: {plot_filename}", flush=True)
                        pickle_bytes = await sandbox_client.download_plot(session_id=active_thread_id, plot_name=plot_filename)
                        if pickle_bytes:
                            figure_object = pickle.loads(pickle_bytes)
                            if hasattr(figure_object, 'to_dict') or isinstance(figure_object, dict):
                                plot_element_name = os.path.basename(plot_filename).rsplit('.', 1)[0]
                                plot_element = cl.Plotly(name=plot_element_name, figure=figure_object, display="inline")
                                collected_plot_elements_this_turn.append(plot_element)
                                processed_plot_filenames_this_turn.add(plot_filename)
                                print(f"[DEBUG PLOT FALLBACK] Added plot '{plot_filename}' from lg_agent_state.", flush=True)
                            else: print(f"[WARN PLOT FALLBACK] Invalid figure from state: {plot_filename}", flush=True)
                        else: print(f"[WARN PLOT FALLBACK] Failed to download from state: {plot_filename}", flush=True)
                    except Exception as e_plot_state:
                        print(f"[ERROR PLOT FALLBACK] Error with {plot_filename} from state: {e_plot_state}", flush=True)
                        import traceback; traceback.print_exc(file=sys.stdout)

        except Exception as e_stream_outer:
            print(f"[ERROR CL STREAM OUTER] Exception during agent stream processing: {e_stream_outer}", flush=True)
            import traceback; traceback.print_exc(file=sys.stdout)
            # Error will be handled by UI updates in 'finally'

        finally:
            # Finalize UI elements robustly
            if not assistant_final_msg.id and not streamed_text_content:
                assistant_final_msg.content = "Traitement terminé."
            try:
                if not assistant_final_msg.id: await assistant_final_msg.send()
                else: await assistant_final_msg.update()
            except Exception as e_ui: print(f"[ERROR CL UI] Final assistant msg update failed: {e_ui}", flush=True)

            if collected_plot_elements_this_turn:
                print(f"[DEBUG PLOT FINAL ATTACH] Attaching {len(collected_plot_elements_this_turn)} plots.", flush=True)
                if not assistant_final_msg.id:
                    try: await assistant_final_msg.send() # Send if not already
                    except Exception as e_ui_send: print(f"[ERROR CL UI] Send before elements failed: {e_ui_send}", flush=True)
                if assistant_final_msg.id:
                    assistant_final_msg.elements = collected_plot_elements_this_turn
                    try: await assistant_final_msg.update()
                    except Exception as e_ui_elem: print(f"[ERROR CL UI] Update with elements failed: {e_ui_elem}", flush=True)
            
            agent_processing_step.output = streamed_text_content or assistant_final_msg.content or "Traitement terminé."
            try: await agent_processing_step.update()
            except Exception as e_ui_step: print(f"[ERROR CL UI] Agent proc step final update failed: {e_ui_step}", flush=True)


    # --- Update cl.user_session's lg_agent_state with the state *after* the graph run ---
    if final_graph_state_dict and isinstance(final_graph_state_dict, dict):
        raw_messages = final_graph_state_dict.get("messages", [])
        rebuilt_messages = []
        # ... (Your message reconstruction logic - seems okay)
        for m_data in raw_messages:
            if isinstance(m_data, (HumanMessage, AIMessage, ToolMessage)): rebuilt_messages.append(m_data)
            elif isinstance(m_data, dict):
                m_type = m_data.get("type","").lower(); m_content = m_data.get("content"); m_id = m_data.get("id", str(uuid.uuid4()))
                if "human" in m_type: rebuilt_messages.append(HumanMessage(content=m_content, id=m_id))
                elif "ai" in m_type: rebuilt_messages.append(AIMessage(content=m_content, id=m_id, tool_calls=m_data.get("tool_calls",[])))
                elif "tool" in m_type: rebuilt_messages.append(ToolMessage(content=m_content, id=m_id, name=m_data.get("name"), tool_call_id=m_data.get("tool_call_id")))

        final_messages_for_state_tuple = remove_duplicate_messages(ensure_message_ids(rebuilt_messages))
        final_messages_for_state = final_messages_for_state_tuple[0] if isinstance(final_messages_for_state_tuple, tuple) else final_messages_for_state_tuple
        
        # Construct the new state for the session, taking all fields from final_graph_state_dict
        # and then overriding specific ones like messages, and crucial IDs.
        new_session_state = {
            **final_graph_state_dict,
            "messages": final_messages_for_state,
            "conversation_id": active_thread_id,
            "session_id": active_thread_id,
            "partner": partner_name,
            # Ensure output_image_paths from our session-managed lg_agent_state is preserved if final_graph_state_dict doesn't have it
            # or if we want our accumulated one to be the source of truth for the *next* turn's lg_agent_state.
            # This can be tricky. The checkpointer saves based on final_graph_state_dict.
            # For simplicity, let's assume final_graph_state_dict should contain the definitive output_image_paths if the graph manages it.
            # If not, you might need to merge:
            # "output_image_paths": list(set(final_graph_state_dict.get("output_image_paths", []) + lg_agent_state.get("output_image_paths", [])))
        }
        # If output_image_paths is NOT reliably in final_graph_state_dict but you updated it in your session's lg_agent_state:
        if "output_image_paths" not in new_session_state and lg_agent_state and "output_image_paths" in lg_agent_state:
             new_session_state["output_image_paths"] = lg_agent_state["output_image_paths"]


        if "input_data" in new_session_state and new_session_state["input_data"]:
            new_session_state["input_data"] = [ # type: ignore
                InputData(**data) if isinstance(data, dict) and not isinstance(data, InputData) else data 
                for data in new_session_state["input_data"] # type: ignore
            ]
        
        cl.user_session.set("lg_agent_state", new_session_state) # type: ignore
        print(f"[DEBUG CL SESSION_STATE] Updated user session lg_agent_state from final graph state. Msgs: {len(new_session_state.get('messages',[]))}", flush=True)
        # For detailed state view:
        # print(f"[DEBUG CL SESSION_STATE DUMP]: {pprint.pformat(serialise_state(new_session_state), width=120)}", flush=True)

    elif lg_agent_state: # Fallback if no final_graph_state_dict
        if not any(getattr(m, 'id', None) == human_message_obj.id for m in lg_agent_state.get("messages", [])):
            lg_agent_state.setdefault("messages", []).append(human_message_obj) # type: ignore
            cl.user_session.set("lg_agent_state", lg_agent_state)
        print(f"[WARN CL SESSION_STATE] No final_graph_state_dict. Used old session state.", flush=True)
    else:
        print(f"[ERROR CL SESSION_STATE] lg_agent_state is None and no final_graph_state_dict.", flush=True)

    print(f"[DEBUG CL] === Turn End for Thread: {active_thread_id} ===\n", flush=True)