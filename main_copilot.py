import os
import uuid
import json
import asyncio
from typing import Dict, List, Any, Optional, TypedDict, Annotated
from dataclasses import dataclass
from operator import or_, add

# <PERSON><PERSON><PERSON><PERSON> and LangGraph imports
from langchain_core.messages import SystemMessage, HumanMessage, ToolMessage
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.messages.utils import count_tokens_approximately
from langgraph.prebuilt import create_react_agent
from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver
from langgraph.graph.message import add_messages
from langgraph.graph import StateGraph, END

# Model and tool imports
from model import model, summary_model, get_prompt_for_partner
from tools import complete_python_task, data_retriever
from summary import pre_model_hook, ensure_message_ids, remove_duplicate_messages

#import mlflow
   # Set the experiment name
#mlflow.set_experiment("test_partners_implementation")
#mlflow.langchain.autolog()

# Database config (use environment variables)
DB_HOST = os.getenv("POSTGRES_HOST", "localhost")
DB_PORT = os.getenv("POSTGRES_PORT", "5432")
DB_NAME = os.getenv("POSTGRES_DB", "chainlit_db")
DB_USER = os.getenv("POSTGRES_USER", "user_test")
DB_PASSWORD = os.getenv("POSTGRES_PASSWORD", "password_test")
DB_URI_CHECKPOINTER = (
    f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}?sslmode=disable"
)

# ─────────────────────────────────────────────────────────────────────────────
# Data structures
@dataclass
class InputData:
    variable_name: str
    data_path: str
    data_description: str
    sandbox_file_name: str
    def __hash__(self):
        return hash((self.variable_name, self.data_path, self.data_description,self.sandbox_file_name))

class AgentState(TypedDict):
    messages: Annotated[List[Any], add_messages]
    remaining_steps: int
    input_data: Annotated[List[InputData], add]
    intermediate_outputs: Annotated[List[dict], add]
    current_variables: Annotated[Dict[str, Any], or_]
    output_image_paths: Annotated[List[str], add]
    data_description: Annotated[List[str], add]
    generic_parser_request: Annotated[List[Any], add]
    conversation_id: str
    session_id: str
    partner: str
    partner_config: Optional[Dict[str, Any]]
    summary: str
    id_last_summary: Optional[str]
    suggestions: List[str]  # New field for suggestions (overwritten each turn)

# ─────────────────────────────────────────────────────────────────────────────
# Utility: shallow merge of two dicts

def _merge(a: Dict[str, Any], b: Dict[str, Any]) -> Dict[str, Any]:
    return {**a, **b}

# ─────────────────────────────────────────────────────────────────────────────
# Checkpointer factory

def make_postgres_checkpointer():
    """
    Return an async context manager for AsyncPostgresSaver.
    Usage:
        async with make_postgres_checkpointer() as ckpt:
            ...
    """
    return AsyncPostgresSaver.from_conn_string(DB_URI_CHECKPOINTER)

tools = [data_retriever, complete_python_task]
# ─────────────────────────────────────────────────────────────────────────────
# Agent factory

def create_agent(checkpointer, partner: str):
    """
    Return a LangGraph React agent with the given async checkpointer.
    """
    prompt = ChatPromptTemplate.from_messages([
        ("system", get_prompt_for_partner()),
        ("placeholder", "{messages}"),
    ])

    agent = create_react_agent(
        model=model,
        tools=tools,
        prompt=prompt,
        pre_model_hook=pre_model_hook,
        state_schema=AgentState,
        checkpointer=checkpointer,
    )
    return agent

def create_agent_subgraph(checkpointer, partner: str):
    """
    Create the agent as a subgraph (your original agent logic)
    """
    prompt = ChatPromptTemplate.from_messages([
        ("system", get_prompt_for_partner()),
        ("placeholder", "{messages}"),
    ])

    agent = create_react_agent(
        model=model,
        tools=tools,
        prompt=prompt,
        pre_model_hook=pre_model_hook,
        state_schema=AgentState,
        checkpointer=checkpointer,
    )
    return agent

# ─────────────────────────────────────────────────────────────────────────────
# Suggestions node
from typing import Dict

async def generate_suggestions_node(state: AgentState) -> Dict[str, Any]:
    """
    Generate 3 suggestions for next questions based on last 3 messages and summary
    """
    # Get last 3 messages
    last_messages = state["messages"][-3:] if len(state["messages"]) >= 3 else state["messages"]
    
    # Format messages for the prompt
    message_context = ""
    for msg in last_messages:
        if hasattr(msg, 'content'):
            role = "Human" if isinstance(msg, HumanMessage) else "Assistant"
            message_context += f"{role}: {msg.content}\n"
    
    # Include summary if it exists
    summary_context = ""
    if state.get("summary"):
        summary_context = f"Conversation Summary: {state['summary']}\n\n"
    
    # Create prompt for suggestions
    suggestions_prompt = f"""Based on the following conversation context, generate exactly 3 helpful and relevant follow-up questions that the user might want to ask next.

{summary_context}Recent Messages:
{message_context}

Please provide 3 concise, actionable questions that would naturally follow from this conversation. Format your response as a simple list of questions, one per line, without numbering or bullet points."""

    # Get suggestions from the model
    try:
        response = await summary_model.ainvoke([HumanMessage(content=suggestions_prompt)])
        suggestions_text = response.content
        
        # Parse the suggestions (split by lines and clean up)
        suggestions = [
            line.strip() 
            for line in suggestions_text.split('\n') 
            if line.strip() and not line.strip().startswith(('1.', '2.', '3.', '-', '*'))
        ][:3]  # Take only first 3
        
        # Ensure we have exactly 3 suggestions
        while len(suggestions) < 3:
            suggestions.append("What would you like to explore next?")
        print(f"[SUGGESTIONS]:{suggestions}")    
    except Exception as e:
        print(f"Error generating suggestions: {e}")
        # Fallback suggestions
        suggestions = [
            "Can you help me with something else?",
            "What other options do I have?",
            "How can we improve this further?"
        ]
    
    return {"suggestions": suggestions}

# ─────────────────────────────────────────────────────────────────────────────
# Async REPL with streaming

async def interactive(thread_id: Optional[str] = None, partner: str = "oksigen") -> None:
    if thread_id is None:
        thread_id = str(uuid.uuid4())
    print(f"Energy Data Copilot – Thread ID: {thread_id}, Partner: {partner}")

    # create async checkpointer and agent
    async with make_postgres_checkpointer() as ckpt:
        agent = create_agent(ckpt, partner)

        # initial state
        state: AgentState = {
            "conversation_id": thread_id,
            "session_id": thread_id,
            "partner": partner,
            "partner_config": {},
            "messages": [],
            "remaining_steps": 10,
            "input_data": [],
            "intermediate_outputs": [],
            "current_variables": {},
            "output_image_paths": [],
            "data_description": [],
            "generic_parser_request": [],
            "id_last_summary": None,
            "summary": "",
            "suggestions": [],
        }

        config = {"configurable": {"thread_id": thread_id,
                                  "session_id": thread_id,
                                  "partner": partner}}

        # REPL loop
        while True:
            try:
                user_input = input("\n[User]: ")
            except (EOFError, KeyboardInterrupt):
                print("\nExiting – conversation ID:", thread_id)
                return
            if user_input.strip().lower() == "exit":
                print("Good-bye!")
                return

            state["messages"].append(HumanMessage(content=user_input))
            print(f"[Debug] invoking agent – {len(state['messages'])} messages")

            last_chunk = None
            async for chunk in agent.astream({"messages": state["messages"]}, config, stream_mode="updates"):
                last_chunk = chunk
                # incremental assistant output
                if chunk.get("messages"):
                    msg = chunk["messages"][-1]
                    if hasattr(msg, "content") and msg.content:
                        print(msg.content, end="", flush=True)

                # harvest tool outputs
                for m in chunk.get("messages", []):
                    if isinstance(m, ToolMessage):
                        try:
                            payload = m.content if isinstance(m.content, dict) else json.loads(m.content)
                        except Exception:
                            continue
                        if payload.get("input_data"):
                            state["input_data"].extend(payload["input_data"])
                        if payload.get("current_variables"):
                            state["current_variables"] = _merge(
                                state["current_variables"],
                                payload["current_variables"],
                            )
            print()

            # update only messages slice
            if last_chunk and "messages" in last_chunk:
                state["messages"] = last_chunk["messages"]

            # dedupe context
            msgs = ensure_message_ids(state["messages"])
            unique, removed = remove_duplicate_messages(msgs)
            if removed:
                state["messages"] = unique
                print(f"[Debug] deduplicated {len(removed)} msgs")

# ─────────────────────────────────────────────────────────────────────────────
# Main graph factory

def create_main_graph(checkpointer, partner: str):
    """
    Create the main graph with agent as subgraph and suggestions generation
    """
    # Create the agent subgraph
    agent_subgraph = create_agent_subgraph(checkpointer, partner)
    
    # Create the main graph
    main_graph = StateGraph(AgentState)
    
    # Add the agent as a subgraph node
    main_graph.add_node("agent", agent_subgraph)
    
    # Add the suggestions generation node
    main_graph.add_node("generate_suggestions", generate_suggestions_node)
    
    # Define the flow: input -> agent -> suggestions -> end
    main_graph.set_entry_point("agent")
    main_graph.add_edge("agent", "generate_suggestions")
    main_graph.add_edge("generate_suggestions", END)
    
    # Compile the graph
    compiled_graph = main_graph.compile(checkpointer=checkpointer)
    
    return compiled_graph

# ─────────────────────────────────────────────────────────────────────────────
# Usage example

async def run_enhanced_agent(user_input: str, config: dict):
    """
    Example of how to use the enhanced agent
    """
    async with make_postgres_checkpointer() as checkpointer:
        # Create the main graph
        graph = create_main_graph(checkpointer, partner=config.get("partner", "your_partner_name"))
        
        # Prepare initial state
        initial_state = {
            "messages": [HumanMessage(content=user_input)],
            "remaining_steps": 10,
            "input_data": [],
            "intermediate_outputs": [],
            "current_variables": {},
            "output_image_paths": [],
            "data_description": [],
            "generic_parser_request": [],
            "conversation_id": config.get("conversation_id", ""),
            "session_id": config.get("session_id", ""),
            "partner": config.get("partner", ""),
            "partner_config": config.get("partner_config"),
            "summary": config.get("summary", ""),
            "id_last_summary": config.get("id_last_summary"),
            "suggestions": []  # Will be populated by the suggestions node
        }
        
        # Run the graph
        result = await graph.ainvoke(initial_state, config=config)
        
        return result

# ─────────────────────────────────────────────────────────────────────────────
# Entry-point
if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description="Energy Data Copilot (CLI Mode)")
    parser.add_argument("--thread-id", type=str, help="Thread ID to resume")
    parser.add_argument("--partner", type=str, default="oksigen", help="Partner code")
    args = parser.parse_args()

    asyncio.run(interactive(thread_id=args.thread_id, partner=args.partner))



